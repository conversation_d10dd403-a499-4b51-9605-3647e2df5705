import React, { useState } from 'react';
import './AppDetails.css';

const AppDetails = ({
  app,
  context,
  onBack,
  onAddToMyApps,
  onRemoveFromMyApps,
  onOpenApp,
  isInMyApps
}) => {
  const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(false);

  if (!app) return null;

  const handlePrimaryAction = () => {
    if (context === 'myApps') {
      // Open the app in chat
      onOpenApp(app);
    } else {
      // Add to My Apps
      onAddToMyApps(app);
    }
  };

  const handleSecondaryAction = () => {
    if (context === 'myApps') {
      // Remove from My Apps
      onRemoveFromMyApps(app);
    } else if (app.url) {
      // Open external URL
      window.open(app.url, '_blank', 'noopener,noreferrer');
    }
  };

  const toggleDescription = () => {
    setIsDescriptionExpanded(!isDescriptionExpanded);
  };

  const renderAppIcon = () => {
    if (app.icon && (app.icon.endsWith('.png') || app.icon.endsWith('.jpg') || app.icon.endsWith('.svg'))) {
      return (
        <img
          src={app.icon}
          alt={`${app.name} logo`}
          className="app-details__icon-image"
          onError={(e) => {
            // Replace with fallback icon if image fails to load
            e.target.style.display = 'none';
            e.target.nextSibling.style.display = 'flex';
          }}
        />
      );
    }
    return null;
  };

  const renderFallbackIcon = () => (
    <div className="app-details__icon-fallback">
      <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
        <rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" strokeWidth="2" fill="var(--card-bg)"/>
        <circle cx="12" cy="12" r="3" fill="currentColor"/>
      </svg>
    </div>
  );

  const getDisplayDescription = () => {
    if (!app.description) return '';
    
    const cleanDescription = app.description.trim();
    if (cleanDescription.length <= 100) {
      return cleanDescription;
    }
    
    if (isDescriptionExpanded) {
      return cleanDescription;
    }
    
    return cleanDescription.substring(0, 100) + '...';
  };

  const shouldShowToggle = () => {
    return app.description && app.description.trim().length > 100;
  };

  return (
    <div className="app-details">
      {/* Header with back button */}
      <div className="app-details__header">
        <button
          className="app-details__back-btn"
          onClick={onBack}
          type="button"
          aria-label="Back to apps"
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <polyline points="15,18 9,12 15,6"/>
          </svg>
          <span>Back</span>
        </button>
      </div>

      {/* Main content */}
      <div className="app-details__content">
        {/* App icon */}
        <div className="app-details__icon">
          {renderAppIcon()}
          {renderFallbackIcon()}
        </div>

        {/* App information */}
        <div className="app-details__info">
          <h2 className="app-details__name">{app.name}</h2>
          
          {app.version && (
            <div className="app-details__version">
              <span className="app-details__version-label">Version:</span>
              <span className="app-details__version-value">{app.version}</span>
            </div>
          )}

          {app.category && (
            <div className="app-details__category">
              <span className="app-details__category-label">Category:</span>
              <span className="app-details__category-value">{app.category}</span>
            </div>
          )}

          {app.description && (
            <div className="app-details__description">
              <h3 className="app-details__description-title">Description</h3>
              <p className="app-details__description-text">
                {getDisplayDescription()}
              </p>
              {shouldShowToggle() && (
                <button
                  className="app-details__description-toggle"
                  onClick={toggleDescription}
                  type="button"
                >
                  {isDescriptionExpanded ? 'Show less' : 'Show more'}
                </button>
              )}
            </div>
          )}

          {/* Action buttons */}
          <div className="app-details__actions">
            {context === 'myApps' ? (
              <>
                {/* Open button for My Apps */}
                <button
                  className="app-details__open-btn"
                  onClick={handlePrimaryAction}
                  type="button"
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"/>
                    <polyline points="15,3 21,3 21,9"/>
                    <line x1="10" y1="14" x2="21" y2="3"/>
                  </svg>
                  Open
                </button>

                {/* Remove from My Apps button */}
                <button
                  className="app-details__remove-btn"
                  onClick={handleSecondaryAction}
                  type="button"
                  title="Remove from My Apps"
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M3 6h18"/>
                    <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/>
                    <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>
                  </svg>
                  Remove
                </button>
              </>
            ) : (
              <>
                {/* Add to My Apps button for Store */}
                <button
                  className="app-details__add-btn"
                  onClick={handlePrimaryAction}
                  type="button"
                  disabled={isInMyApps}
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M12 5v14"/>
                    <path d="M5 12h14"/>
                  </svg>
                  {isInMyApps ? 'Added to My Apps' : 'Add to My Apps'}
                </button>

                {/* External link button for Store apps */}
                {app.url && (
                  <button
                    className="app-details__external-btn"
                    onClick={handleSecondaryAction}
                    type="button"
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"/>
                      <polyline points="15,3 21,3 21,9"/>
                      <line x1="10" y1="14" x2="21" y2="3"/>
                    </svg>
                    Visit Website
                  </button>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AppDetails;
