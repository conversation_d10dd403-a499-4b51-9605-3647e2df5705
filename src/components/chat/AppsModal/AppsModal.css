/* Apps Modal Custom Sizing */
.apps-modal {
  min-width: 80vw !important;
  min-height: 80vh;
  padding: 26px !important;
}
.apps-modal .modal__header{
  padding: 0 !important;
}
.apps-modal .modal__content {
  padding: 0 !important;
  height: calc(100% - 80px); /* Subtract header height */
  overflow-y: auto;
}

/* Apps Modal Content */
.apps-modal__content {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

/* Tabs */
.apps-modal__tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 20px;
}

.apps-modal__tab {
  flex: 1;
  padding: 12px 16px;
  background: transparent;
  border: none;
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
}

.apps-modal__tab:hover {
  color: var(--text-primary);
  background: var(--card-bg);
}

.apps-modal__tab--active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  background: var(--card-bg);
}

/* Tab Content */
.apps-modal__tab-content {
  flex: 1;
  overflow-y: auto;
}

/* Apps Grid Layout */
.apps-modal__grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 24px;
  padding: 16px 0;
  justify-items: center;
  animation: appsGridFadeIn 0.3s ease-out;
}

@keyframes appsGridFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Individual App Item */
.apps-modal__app-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12px 8px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
  max-width: 100px;
  text-align: center;
  background: transparent;
  border: 2px solid transparent;
}

.apps-modal__app-item:hover {
  background: var(--card-bg);
  border-color: var(--border-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.apps-modal__app-item:active {
  transform: translateY(0);
  background: var(--secondary-bg);
}

/* App Icon Container */
.apps-modal__app-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  margin-bottom: 8px;
  border-radius: 12px;
  background: var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.apps-modal__app-item:hover .apps-modal__app-icon {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: scale(1.05);
  background: transparent;
}

/* App Name */
.apps-modal__app-name {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-primary);
  line-height: 1.3;
  word-wrap: break-word;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* Placeholder Content */
.apps-modal__placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: var(--text-secondary);
}

.apps-modal__placeholder-icon {
  margin-bottom: 24px;
  color: var(--text-muted);
}

.apps-modal__placeholder-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 16px 0;
}

.apps-modal__placeholder-description {
  font-size: 16px;
  line-height: 1.6;
  color: var(--text-secondary);
  margin: 0;
  max-width: 400px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .apps-modal {
    width: 95vw;
    height: 95vh;
    margin: 0 !important;
  }

  .apps-modal .modal__content {
    height: calc(100% - 70px); /* Adjust for smaller header on mobile */
  }

  .apps-modal__grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    padding: 12px 0;
  }

  .apps-modal__app-item {
    padding: 12px 5px;
    min-width: 70px;
    max-width: 85px;
  }

  .apps-modal__app-icon {
    width: 48px;
    height: 48px;
    margin-bottom: 6px;
  }

  .apps-modal__app-name {
    font-size: 11px;
  }

  .apps-modal__placeholder-title {
    font-size: 20px;
  }

  .apps-modal__placeholder-description {
    font-size: 14px;
  }

  .apps-modal__placeholder-icon {
    margin-bottom: 20px;
  }

  .apps-modal__tabs {
    margin-bottom: 16px;
  }

  .apps-modal__tab {
    padding: 10px 12px;
    font-size: 13px;
  }
}

/* Tablet Design */
@media (min-width: 769px) and (max-width: 1024px) {
  .apps-modal__grid {
    grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
    gap: 22px;
  }

  .apps-modal__app-item {
    min-width: 85px;
    max-width: 95px;
  }
}
