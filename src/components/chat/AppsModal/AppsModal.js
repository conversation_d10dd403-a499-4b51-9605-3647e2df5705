import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Modal from '../../common/Modal';
import AppDetails from './AppDetails';
import appsData from '../../../data/apps.json';
import { getMyAppsWithDetails, addToMyApps, removeFromMyApps, isInMyApps } from '../../../utils/myAppsStorage';
import './AppsModal.css';



const AppsModal = ({ isOpen, onClose }) => {
  const [storeApps, setStoreApps] = useState([]);
  const [myApps, setMyApps] = useState([]);
  const [selectedApp, setSelectedApp] = useState(null);
  const [showAppDetails, setShowAppDetails] = useState(false);
  const [activeTab, setActiveTab] = useState('myApps');
  const [currentContext, setCurrentContext] = useState('myApps'); // 'myApps' or 'store'
  const navigate = useNavigate();

  useEffect(() => {
    // Load apps from JSON data
    const store = appsData.store || [];
    setStoreApps(store);

    // Load My Apps with details
    const myAppsWithDetails = getMyAppsWithDetails(store);
    setMyApps(myAppsWithDetails);
  }, []);

  // Refresh My Apps when modal opens
  useEffect(() => {
    if (isOpen) {
      const myAppsWithDetails = getMyAppsWithDetails(storeApps);
      setMyApps(myAppsWithDetails);
    }
  }, [isOpen, storeApps]);

  const handleAppClick = (app, context) => {
    setSelectedApp(app);
    setCurrentContext(context);
    setShowAppDetails(true);
  };

  const handleBackToApps = () => {
    setShowAppDetails(false);
    setSelectedApp(null);
    setCurrentContext(activeTab);
  };

  const handleModalClose = () => {
    // Reset to apps grid when modal is closed
    setShowAppDetails(false);
    setSelectedApp(null);
    setActiveTab('myApps');
    setCurrentContext('myApps');
    onClose();
  };

  const handleTabChange = (tab) => {
    setActiveTab(tab);
    setCurrentContext(tab);
  };

  const handleAddToMyApps = (app) => {
    const success = addToMyApps(app.id);
    if (success) {
      // Refresh My Apps list
      const myAppsWithDetails = getMyAppsWithDetails(storeApps);
      setMyApps(myAppsWithDetails);

      // Go back to apps grid and switch to My Apps tab
      setShowAppDetails(false);
      setSelectedApp(null);
      setActiveTab('myApps');
      setCurrentContext('myApps');
    }
  };

  const handleRemoveFromMyApps = (app) => {
    const success = removeFromMyApps(app.id);
    if (success) {
      // Refresh My Apps list
      const myAppsWithDetails = getMyAppsWithDetails(storeApps);
      setMyApps(myAppsWithDetails);
    }
  };

  const handleOpenApp = (app) => {
    // Navigate to /chat/app-name (using app id as the name)
    navigate(`/chat/${app.id}`);
    handleModalClose();
  };

  const renderAppIcon = (app) => {
    if (app.icon && (app.icon.endsWith('.png') || app.icon.endsWith('.jpg') || app.icon.endsWith('.svg'))) {
      return (
        <img
          src={app.icon}
          alt={`${app.name} logo`}
          width="48"
          height="48"
          style={{ objectFit: 'contain' }}
          onError={(e) => {
            // Replace with fallback icon if image fails to load
            e.target.outerHTML = `
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                <rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2" fill="var(--card-bg)"/>
                <circle cx="12" cy="12" r="3" fill="currentColor"/>
              </svg>
            `;
          }}
        />
      );
    }

    // Fallback default icon
    return (
      <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
        <rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" strokeWidth="2" fill="var(--card-bg)"/>
        <circle cx="12" cy="12" r="3" fill="currentColor"/>
      </svg>
    );
  };

  const getCurrentApps = () => {
    return activeTab === 'myApps' ? myApps : storeApps;
  };

  const renderTabContent = () => {
    const currentApps = getCurrentApps();

    if (currentApps.length > 0) {
      return (
        <div className="apps-modal__grid">
          {currentApps.map((app) => (
            <div
              key={app.id}
              className="apps-modal__app-item"
              onClick={() => handleAppClick(app, activeTab)}
              title={app.description}
            >
              <div className="apps-modal__app-icon">
                {renderAppIcon(app)}
              </div>
              <span className="apps-modal__app-name">{app.name}</span>
            </div>
          ))}
        </div>
      );
    } else {
      const placeholderContent = activeTab === 'myApps'
        ? {
            title: "No Apps Added Yet",
            description: "Browse the Apps Store to add apps to your collection."
          }
        : {
            title: "Apps Coming Soon",
            description: "We're working on bringing you powerful apps and integrations to enhance your chat experience."
          };

      return (
        <div className="apps-modal__placeholder">
          <div className="apps-modal__placeholder-icon">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
              <rect x="3" y="3" width="7" height="7" rx="1" stroke="currentColor" strokeWidth="2"/>
              <rect x="14" y="3" width="7" height="7" rx="1" stroke="currentColor" strokeWidth="2"/>
              <rect x="3" y="14" width="7" height="7" rx="1" stroke="currentColor" strokeWidth="2"/>
              <rect x="14" y="14" width="7" height="7" rx="1" stroke="currentColor" strokeWidth="2"/>
            </svg>
          </div>
          <h3 className="apps-modal__placeholder-title">{placeholderContent.title}</h3>
          <p className="apps-modal__placeholder-description">
            {placeholderContent.description}
          </p>
        </div>
      );
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleModalClose}
      title={showAppDetails ? selectedApp?.name || "App Details" : "Apps"}
      size="custom"
      className="apps-modal"
      closeOnOverlayClick={true}
    >
      <div className="apps-modal__content">
        {showAppDetails ? (
          <AppDetails
            app={selectedApp}
            context={currentContext}
            onBack={handleBackToApps}
            onAddToMyApps={handleAddToMyApps}
            onRemoveFromMyApps={handleRemoveFromMyApps}
            onOpenApp={handleOpenApp}
            isInMyApps={isInMyApps(selectedApp?.id)}
          />
        ) : (
          <>
            {/* Tabs */}
            <div className="apps-modal__tabs">
              <button
                className={`apps-modal__tab ${activeTab === 'myApps' ? 'apps-modal__tab--active' : ''}`}
                onClick={() => handleTabChange('myApps')}
              >
                My Apps
              </button>
              <button
                className={`apps-modal__tab ${activeTab === 'store' ? 'apps-modal__tab--active' : ''}`}
                onClick={() => handleTabChange('store')}
              >
                Apps Store
              </button>
            </div>

            {/* Tab Content */}
            <div className="apps-modal__tab-content">
              {renderTabContent()}
            </div>
          </>
        )}
      </div>
    </Modal>
  );
};

export default AppsModal;
